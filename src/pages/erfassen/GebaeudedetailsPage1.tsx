import { useState, useEffect, useCallback, useRef, memo } from 'react';
import { useForm, useField } from '@tanstack/react-form';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { z } from 'zod';
import { Link, useNavigate } from '@tanstack/react-router';
import { supabase } from '../../lib/supabase';
import { useCertificate } from '../../contexts/CertificateContext';
import { getAutomaticValues, type CertificateType } from '../../utils/certificateTypeMapping';
import { Breadcrumb } from '../../components/ui/Breadcrumb';
import { usePageVisit } from '../../hooks/usePageVisit';
import { useNavigationState } from '../../hooks/useNavigationState';
import { EnhancedFormField } from '../../components/ui/EnhancedFormField';
import { EnhancedSelectField } from '../../components/ui/EnhancedSelectField';
import { BaujahrFieldWithHint } from '../../components/ui/BaujahrFieldWithHint';
import { ValidationErrorSummary } from '../../components/ui/ValidationErrorSummary';
import { handleZodValidationError, clearFormFieldErrors } from '../../utils/formValidation';

// Define the form schema using Zod
const gebaeudedetailsSchema = z.object({
  // Gebäudedetails Teil 1
  BedarfVerbrauch: z.enum(['V', 'B']).default('V'),
  Anlass: z.enum(['AG_VERMIETUNG', 'AG_AUSHANG', 'AG_SONST']).default('AG_VERMIETUNG'),
  Datenerhebung: z.enum(['0', '1']).default('0'),
  nichtWohnGeb: z.enum(['0', '1']).default('0'),
  isGebaeudehuelle: z.enum(['0', '1']).default('1'),
  // These fields are conditionally required based on certificate type
  Nutzung1_ID: z.string().optional(),
  Nutzung1_Flaeche: z.string().optional(),
  Baujahr: z.string().min(4, 'Baujahr ist erforderlich'),
  Modernisierung: z.string().optional(),
  Wohnfläche: z.string().min(1, 'Wohnfläche ist erforderlich'),
  // These fields are conditionally required for WG/B certificate type
  Raumhöhe: z.string().optional(),
  Volumen: z.string().optional(),
  Wohneinheiten: z.string().min(1, 'Anzahl der Wohneinheiten ist erforderlich'),
  Geschosse: z.string().optional(),
  anbauSituation: z.enum(['0', '1', '2']).optional().default('0'), // Default to 'Freistehend'
  ergaenzendeErlaeuterungen: z.string().optional(),
  baujahrHzErz: z.string().min(1, 'Baujahr Heizung ist erforderlich'),
});

type GebaeudedetailsFormValues = z.infer<typeof gebaeudedetailsSchema>;

// Memoized custom component for baujahrHzErz field with validation
const BaujahrHzErzFieldWithValidation = memo(({
  name,
  label,
  placeholder = '',
  required = true,
  form,
  helpText,
  customError
}: {
  name: keyof GebaeudedetailsFormValues;
  label: string;
  placeholder?: string;
  required?: boolean;
  form: any;
  helpText?: string;
  customError?: string | null;
}) => {
  const field = useField({
    form,
    name: name as any,
  });

  // Use local state for the input value to prevent focus loss
  const [localValue, setLocalValue] = useState(field.state.value ?? '');
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update local value when field value changes (from external sources)
  useEffect(() => {
    setLocalValue(field.state.value ?? '');
  }, [field.state.value]);

  const handleBlur = useCallback(() => {
    // Update the form field with the current local value on blur
    field.handleChange(localValue);
    field.handleBlur();
  }, [field, localValue]);

  // Use local state for immediate UI updates, debounce form updates
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // Update local state immediately for responsive UI
    setLocalValue(value);

    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Debounce form state updates to prevent re-renders during typing
    timeoutRef.current = setTimeout(() => {
      field.handleChange(value);
    }, 300); // 300ms debounce
  }, [field]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Combine form field errors with custom validation error
  const hasFormErrors = field.state.meta.errors.length > 0;
  const hasCustomError = customError !== null;
  const hasErrors = hasFormErrors || hasCustomError;

  // Combine all errors
  const allErrors = [
    ...field.state.meta.errors,
    ...(customError ? [customError] : [])
  ];

  return (
    <div className="mb-4">
      <label htmlFor={field.name} className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>

      <div className="relative">
        <input
          id={field.name}
          name={field.name}
          type="text"
          value={typeof localValue === 'object' && localValue !== null ? '' : String(localValue)}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder={placeholder}
          autoComplete="off"
          spellCheck={false}
          autoCorrect="off"
          autoCapitalize="off"
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 transition-colors ${
            hasErrors
              ? 'border-red-500 focus:ring-red-500 focus:border-red-500 bg-red-50'
              : 'border-gray-300 focus:ring-green-500 focus:border-green-500'
          }`}
          aria-invalid={hasErrors}
          aria-describedby={hasErrors ? `${field.name}-error` : helpText ? `${field.name}-help` : undefined}
        />

        {/* Error icon */}
        {hasErrors && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
        )}
      </div>

      {/* Error messages */}
      {hasErrors && (
        <div id={`${field.name}-error`} className="mt-1">
          {allErrors.map((error, index) => (
            <p key={index} className="text-sm text-red-600 flex items-start">
              <svg className="h-4 w-4 text-red-500 mt-0.5 mr-1 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              {error}
            </p>
          ))}
        </div>
      )}

      {/* Help text */}
      {!hasErrors && helpText && (
        <p id={`${field.name}-help`} className="mt-1 text-sm text-gray-500">
          {helpText}
        </p>
      )}
    </div>
  );
});

BaujahrHzErzFieldWithValidation.displayName = 'BaujahrHzErzFieldWithValidation';

export const GebaeudedetailsPage1 = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string[]>>({});
  const [certificateType, setCertificateType] = useState<CertificateType | null>(null);
  const { activeCertificateId } = useCertificate();
  const { markPageAsVisited } = useNavigationState(certificateType);

  // State for heating system construction year validation
  const [baujahrHzErzError, setBaujahrHzErzError] = useState<string | null>(null);

  // Mark this page as visited for navigation tracking
  usePageVisit('gebaeudedetails1');
  const initialValues: Partial<GebaeudedetailsFormValues> = {
    BedarfVerbrauch: 'V' as const,
    Anlass: 'AG_VERMIETUNG' as const,
    Datenerhebung: '0' as const,
    nichtWohnGeb: '0' as const,
    isGebaeudehuelle: '1' as const,
    Nutzung1_ID: '',
    Nutzung1_Flaeche: '',
    Baujahr: '',
    Modernisierung: '',
    Wohnfläche: '',
    Raumhöhe: '',
    Volumen: '',
    Wohneinheiten: '',
    Geschosse: '',
    anbauSituation: '0', // Default to 'Freistehend' for WG/B certificate type
    ergaenzendeErlaeuterungen: '',
    baujahrHzErz: '',
  };
  const [isLoading, setIsLoading] = useState(true);

  // Fetch certificate type
  const { data: certificateData } = useQuery({
    queryKey: ['energieausweise', 'certificate_type', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('certificate_type')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Fetch existing data
  const { data: existingData, isError, error } = useQuery({
    queryKey: ['energieausweise', 'gebaeudedetails1', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('gebaeudedetails1')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Update certificate type when data is fetched
  useEffect(() => {
    if (certificateData?.certificate_type) {
      const certType = certificateData.certificate_type as CertificateType;
      setCertificateType(certType);
    }
  }, [certificateData]);

  // Set loading to false when data is loaded
  useEffect(() => {
    setIsLoading(false);
  }, [existingData]);

  // Define the mutation for saving data to Supabase
  const saveMutation = useMutation({
    mutationFn: async (data: GebaeudedetailsFormValues) => {
      if (!activeCertificateId) throw new Error('Kein aktives Zertifikat ausgewählt.');

      // Create a copy of the data to modify
      const dataToSave = { ...data };

      // Remove Nutzung1 fields if certificate type is not NWG/V
      if (certificateType !== 'NWG/V') {
        delete dataToSave.Nutzung1_ID;
        delete dataToSave.Nutzung1_Flaeche;
      }

      // Remove WG/B specific fields if certificate type is not WG/B
      if (certificateType !== 'WG/B') {
        delete (dataToSave as any).Raumhöhe;
        delete (dataToSave as any).Volumen;
        delete (dataToSave as any).Geschosse;
        delete (dataToSave as any).anbauSituation;
      }



      const { data: result, error } = await supabase
        .from('energieausweise')
        .update({
          gebaeudedetails1: dataToSave,
          updated_at: new Date().toISOString(),
        })
        .eq('id', activeCertificateId)
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: async () => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['energieausweise', activeCertificateId] });
      queryClient.invalidateQueries({ queryKey: ['energieausweise', 'gebaeudedetails1', activeCertificateId] });

      // Update navigation state to mark next page as current
      await markPageAsVisited('gebaeudedetails2');

      // Navigate to the next page
      navigate({ to: '/erfassen/gebaeudedetails2' });
    },
    onError: (error) => {
      setSubmitError(`Fehler beim Speichern: ${error.message}`);
    },
  });

  // Validation function for heating system construction year
  const validateBaujahrHzErz = useCallback((baujahrHzErz: string, baujahr: string) => {
    if (!baujahrHzErz || !baujahr) {
      return null; // No validation if either field is empty
    }

    const baujahrHzErzYear = parseInt(baujahrHzErz, 10);
    const baujahrYear = parseInt(baujahr, 10);

    if (isNaN(baujahrHzErzYear) || isNaN(baujahrYear)) {
      return null; // No validation if either field is not a valid number
    }

    if (baujahrHzErzYear < baujahrYear) {
      return 'Das Baujahr der Heizung kann nicht vor dem Baujahr des Gebäudes liegen';
    }

    return null;
  }, []);

  // Create the form
  const form = useForm({
    defaultValues: initialValues,
    onSubmit: async ({ value }) => {
      setSubmitError(null);
      setValidationErrors({});

      // Clear any existing field errors
      const fieldNames = Object.keys(gebaeudedetailsSchema.shape);
      clearFormFieldErrors(form, fieldNames);

      // Clear custom validation error
      setBaujahrHzErzError(null);

      // Validate heating system construction year
      const baujahrHzErzValidationError = validateBaujahrHzErz(
        value.baujahrHzErz || '',
        value.Baujahr || ''
      );

      if (baujahrHzErzValidationError) {
        setBaujahrHzErzError(baujahrHzErzValidationError);
        setSubmitError('Bitte korrigieren Sie die markierten Eingaben.');
        return;
      }

      // Automatically set BedarfVerbrauch and nichtWohnGeb based on certificate type
      const automaticValues = certificateType ? getAutomaticValues(certificateType) : {};
      const valueWithAutomaticFields = {
        ...value,
        ...automaticValues
      };

      try {
        // Validate with Zod schema
        const validatedValues = gebaeudedetailsSchema.parse(valueWithAutomaticFields);

        // Additional validation for NWG/V certificate type
        if (certificateType === 'NWG/V') {
          if (!validatedValues.Nutzung1_ID) {
            setSubmitError('Nutzung1 ID ist für Nichtwohngebäude erforderlich');
            return;
          }
          if (!validatedValues.Nutzung1_Flaeche) {
            setSubmitError('Nutzung1 Fläche ist für Nichtwohngebäude erforderlich');
            return;
          }
        }

        // Additional validation for WG/B certificate type
        if (certificateType === 'WG/B') {
          if (!validatedValues.Raumhöhe) {
            setSubmitError('Raumhöhe ist für Bedarfsausweise erforderlich');
            return;
          }
          if (!validatedValues.Volumen) {
            setSubmitError('Gebäudevolumen ist für Bedarfsausweise erforderlich');
            return;
          }
          if (!validatedValues.Geschosse) {
            setSubmitError('Anzahl der Geschosse ist für Bedarfsausweise erforderlich');
            return;
          }
          // Fix: Check for undefined/null instead of falsy check
          // This prevents '0' (Freistehend) from being treated as invalid
          if (validatedValues.anbauSituation === undefined ||
              validatedValues.anbauSituation === null) {
            setSubmitError('Anbausituation ist für Bedarfsausweise erforderlich');
            return;
          }
        }

        saveMutation.mutate(validatedValues);
      } catch (validationError) {
        if (validationError instanceof z.ZodError) {
          // Use the improved validation error handling
          const { fieldErrors, summaryMessage } = handleZodValidationError(validationError, form);
          setValidationErrors(fieldErrors);
          setSubmitError(summaryMessage);

          // Log for debugging
          console.error("Form validation error:", validationError.flatten().fieldErrors);
        } else {
          setSubmitError("Ein unerwarteter Validierungsfehler ist aufgetreten.");
        }
      }
    },
  });

  // Update form values when data is loaded - use setFieldValue instead of form.reset to avoid re-rendering
  useEffect(() => {
    if (existingData?.gebaeudedetails1) {
      const savedData = existingData.gebaeudedetails1 as Partial<GebaeudedetailsFormValues>;
      const automaticValues = certificateType ? getAutomaticValues(certificateType) : {};
      const combinedData = { ...savedData, ...automaticValues };

      // Set individual field values instead of resetting the entire form
      Object.entries(combinedData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          form.setFieldValue(key as keyof GebaeudedetailsFormValues, value);
        }
      });
    }
  }, [existingData, certificateType, form]); // Depend on existingData, certificateType, and form

  // Get field states for reactive validation
  const baujahrField = useField({ form, name: 'Baujahr' });
  const baujahrHzErzField = useField({ form, name: 'baujahrHzErz' });

  // Auto-population logic for baujahrHzErz field
  useEffect(() => {
    const baujahrValue = baujahrField.state.value || '';
    const baujahrHzErzValue = baujahrHzErzField.state.value || '';

    // Only auto-populate if:
    // 1. Baujahr has a value (and it's a valid 4-digit year)
    // 2. baujahrHzErz is currently empty/unset
    // 3. The Baujahr value is different from the current baujahrHzErz value (to avoid infinite loops)
    if (baujahrValue &&
        baujahrValue.length === 4 &&
        /^\d{4}$/.test(baujahrValue) &&
        !baujahrHzErzValue &&
        baujahrValue !== baujahrHzErzValue) {

      // Set baujahrHzErz to the same value as Baujahr
      form.setFieldValue('baujahrHzErz', baujahrValue);
    }
  }, [baujahrField.state.value, baujahrHzErzField.state.value, form]);

  // Debounced reactive validation for heating system construction year
  const validationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const baujahrValue = baujahrField.state.value || '';
    const baujahrHzErzValue = baujahrHzErzField.state.value || '';

    // Clear existing timeout
    if (validationTimeoutRef.current) {
      clearTimeout(validationTimeoutRef.current);
    }

    // Debounce validation to prevent excessive updates during typing
    validationTimeoutRef.current = setTimeout(() => {
      const validationError = validateBaujahrHzErz(baujahrHzErzValue, baujahrValue);
      setBaujahrHzErzError(validationError);
    }, 500); // 500ms debounce for validation

    // Cleanup timeout on unmount or dependency change
    return () => {
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }
    };
  }, [baujahrField.state.value, baujahrHzErzField.state.value, validateBaujahrHzErz]);

  // Helper component for form fields
  const FormField = ({
    name,
    label,
    type = 'text',
    placeholder = '',
    required = true
  }: {
    name: keyof GebaeudedetailsFormValues;
    label: string;
    type?: string;
    placeholder?: string;
    required?: boolean;
  }) => {
    const { state, handleChange, handleBlur } = useField({
      name,
      form,
    });

    return (
      <div className="mb-4">
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <input
          id={name}
          name={name}
          type={type}
          value={typeof state.value === 'boolean' ? String(state.value) : (state.value ?? '')}
          onChange={(e) => handleChange(e.target.value)}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
            state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
          }`}
        />
        {state.meta.errors.length > 0 && (
          <p className="mt-1 text-sm text-red-500">{state.meta.errors.join(', ')}</p>
        )}
      </div>
    );
  };

  // Helper component for select fields
  const SelectField = ({
    name,
    label,
    options,
    required = true,
    placeholder = 'Bitte wählen...'
  }: {
    name: keyof GebaeudedetailsFormValues;
    label: string;
    options: { value: string; label: string }[];
    required?: boolean;
    placeholder?: string;
  }) => {
    const { state, handleChange, handleBlur } = useField({
      name,
      form,
    });

    return (
      <div className="mb-4">
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <select
          id={name}
          name={name}
          value={typeof state.value === 'boolean' ? String(state.value) : (state.value ?? '')}
          onChange={(e) => handleChange(e.target.value)}
          onBlur={handleBlur}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
            state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
          }`}
        >
          {/* Only show placeholder if no value is selected and field is not pre-populated */}
          {(!state.value || state.value === '') && (
            <option value="">{placeholder}</option>
          )}
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {state.meta.errors.length > 0 && (
          <p className="mt-1 text-sm text-red-500">{state.meta.errors.join(', ')}</p>
        )}
      </div>
    );
  };



  return (
    <div className="max-w-4xl mx-auto">
      {/* Breadcrumb Navigation */}
      <Breadcrumb className="mb-6" />

      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Gebäudedetails erfassen (Teil 1)
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        Bitte geben Sie die grundlegenden Gebäudedaten ein.
      </p>


      {isLoading ? (
        <div className="bg-white shadow-md rounded-lg p-6 flex justify-center items-center h-64">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500 mb-2"></div>
            <p className="text-gray-600">Daten werden geladen...</p>
          </div>
        </div>
      ) : (
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="bg-white shadow-md rounded-lg p-6"
        >
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
            Allgemeine Gebäudeinformationen
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <EnhancedSelectField
              name="Anlass"
              label="Anlass"
              options={[
                { value: 'AG_VERMIETUNG', label: 'Vermietung/Verkauf' },
                { value: 'AG_SONST', label: 'Modernisierung/Erweiterung' },
                { value: 'AG_AUSHANG', label: 'Sonstiges (Aushang)' },
              ]}
              form={form}
              helpText="Grund für die Erstellung des Energieausweises"
            />

            <EnhancedSelectField
              name="Datenerhebung"
              label="Datenerhebung durch"
              options={[
                { value: '0', label: 'Eigentümer' },
                { value: '1', label: 'Aussteller' },
              ]}
              form={form}
              helpText="Wer hat die Daten für den Energieausweis erhoben"
            />

            <EnhancedSelectField
              name="isGebaeudehuelle"
              label="Gebäudehülle"
              options={[
                { value: '1', label: 'Ein Gebäude' },
                { value: '0', label: 'Mehrere Gebäude' },
              ]}
              form={form}
              helpText="Anzahl der Gebäude in der Bewertung"
            />
          </div>
        </div>

        {certificateType === 'NWG/V' && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Nutzungsdaten
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <EnhancedFormField
                name="Nutzung1_ID"
                label="Nutzung1 ID (für Nichtwohngebäude)"
                placeholder="z.B. 91"
                required={certificateType === 'NWG/V'}
                form={form}
                helpText="Nutzungsart-ID nach DIN V 18599"
              />

              <EnhancedFormField
                name="Nutzung1_Flaeche"
                label="Nutzung1 Fläche in m² (für Nichtwohngebäude)"
                placeholder="z.B. 1000"
                required={certificateType === 'NWG/V'}
                form={form}
                helpText="Nutzfläche in Quadratmetern"
              />
            </div>
          </div>
        )}

        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
            Gebäudedaten
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <BaujahrFieldWithHint
              name="Baujahr"
              label="Baujahr"
              placeholder="z.B. 1975"
              required={true}
              form={form}
              helpText="Jahr der Fertigstellung des Gebäudes"
              certificateType={certificateType}
            />

            <EnhancedFormField
              name="Modernisierung"
              label="Jahr der energetischen Sanierung"
              placeholder="z.B. 2000"
              required={false}
              form={form}
              helpText="Optional: Jahr der letzten energetischen Modernisierung"
            />

            <EnhancedFormField
              name="Wohnfläche"
              label="Wohnfläche in m²"
              placeholder="z.B. 430"
              required={true}
              form={form}
              helpText="Gesamte Wohnfläche nach Wohnflächenverordnung"
            />

            {certificateType === 'WG/B' && (
              <EnhancedFormField
                name="Raumhöhe"
                label="Durchschnittliche Raumhöhe in m"
                placeholder="z.B. 2.5"
                required={certificateType === 'WG/B'}
                form={form}
                helpText="Durchschnittliche lichte Raumhöhe in Metern"
              />
            )}

            {certificateType === 'WG/B' && (
              <EnhancedFormField
                name="Volumen"
                label="Gebäudevolumen in m³"
                placeholder="Wird ggf. automatisch berechnet"
                required={certificateType === 'WG/B'}
                form={form}
                helpText="Beheiztes Gebäudevolumen in Kubikmetern"
              />
            )}

            <FormField
              name="Wohneinheiten"
              label="Anzahl Wohneinheiten"
              placeholder="z.B. 3"
              required={true}
            />

            {certificateType === 'WG/B' && (
              <FormField
                name="Geschosse"
                label="Anzahl der Geschosse"
                placeholder="z.B. 3"
                required={certificateType === 'WG/B'}
              />
            )}

            {certificateType === 'WG/B' && (
              <SelectField
                name="anbauSituation"
                label="Anbausituation"
                options={[
                  { value: '0', label: 'Freistehend' },
                  { value: '1', label: 'Einseitig angebaut' },
                  { value: '2', label: 'Zweiseitig angebaut' },
                ]}
                required={certificateType === 'WG/B'}
              />
            )}

            <div className="md:col-span-2">
              <FormField
                name="ergaenzendeErlaeuterungen"
                label="Ergänzende Erläuterungen"
                placeholder="Zusätzliche Informationen zum Gebäude"
                required={false}
              />
            </div>

            <BaujahrHzErzFieldWithValidation
              name="baujahrHzErz"
              label="Baujahr Heizung"
              placeholder="z.B. 1980"
              required={true}
              form={form}
              helpText="Jahr der Installation oder Erneuerung des Wärmeerzeugers (Heizung)"
              customError={baujahrHzErzError}
            />
          </div>
        </div>

        {submitError && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {submitError}
          </div>
        )}

        {/* Validation Error Summary */}
        {Object.keys(validationErrors).length > 0 && (
          <ValidationErrorSummary errors={validationErrors} />
        )}
        
        <div className="flex justify-between mt-8">
          <Link
            to="/erfassen/objektdaten"
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
          >
            Zurück
          </Link>
          <button
            type="submit"
            disabled={form.state.isSubmitting || saveMutation.isPending}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:bg-green-300"
          >
            {form.state.isSubmitting || saveMutation.isPending ? 'Wird gespeichert...' : 'Weiter'}
          </button>
        </div>
      </form>
      )}

      {isError && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <p>Fehler beim Laden der Daten: {error instanceof Error ? error.message : 'Unbekannter Fehler'}</p>
          <p className="mt-2">Bitte versuchen Sie es später erneut oder kontaktieren Sie den Support.</p>
        </div>
      )}
    </div>
  );
};